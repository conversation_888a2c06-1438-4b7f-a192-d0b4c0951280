extends Control

@onready var find_game_button = $VBoxContainer/FindGameButton
@onready var status_label = $VBoxContainer/StatusLabel
@onready var back_button = $VBoxContainer/BackButton
@onready var matchmaker_request = $MatchmakerRequest

const MATCHMAKER_URL = "http://localhost:8000"
const APP_ID = 436480

var game_client = WebSocketPeer.new()
var player_nodes = {}

func _ready():
	if not Steam.isSteamRunning():
		status_label.text = "Ошибка: не удалось запустить Steam. Перезапустите Steam и игру."
		find_game_button.disabled = true
		return

	find_game_button.pressed.connect(_on_find_game_pressed)
	back_button.pressed.connect(_on_back_button_pressed)
	matchmaker_request.request_completed.connect(_on_matchmaker_request_completed)
	Steam.get_auth_session_ticket_response.connect(_on_auth_ticket_response)

func _process(_delta):
	game_client.poll()

	var state = game_client.get_ready_state()
	if state == WebSocketPeer.STATE_OPEN:
		_send_player_input()
		_handle_websocket_messages()
	elif state == WebSocketPeer.STATE_CLOSED:
		var code = game_client.get_close_code()
		var reason = game_client.get_close_reason()
		status_label.text = "Отключено от сервера. Код: %d, Причина: %s" % [code, reason]

func _on_find_game_pressed():
	status_label.text = "Получаем билет от Steam..."
	find_game_button.disabled = true
	back_button.disabled = true
	Steam.getAuthSessionTicket()

func _on_auth_ticket_response(auth_ticket: int, result: int) -> void:
	print("Ответ билета аутентификации: %s, результат: %s" % [auth_ticket, result])
	if result == Steam.RESULT_OK:
		status_label.text = "Отправляем запрос на сервер..."
		var my_steam_id = Steam.getSteamID()

		var body = JSON.stringify({
			"ticket": auth_ticket,
			"steamId": str(my_steam_id)
		})

		var headers = ["Content-Type: application/json"]
		matchmaker_request.request(MATCHMAKER_URL, headers, HTTPClient.METHOD_POST, body)
	else:
		status_label.text = "Ошибка: не удалось получить билет от Steam."
		find_game_button.disabled = false
		back_button.disabled = false

func _on_matchmaker_request_completed(_result, response_code, _headers, body):
	print("Ответ матчмейкера: %s, код: %s" % [body.get_string_from_utf8(), response_code])
	if response_code == 200:
		var json = JSON.parse_string(body.get_string_from_utf8())
		if json and json.has("gameServerUrl"):
			status_label.text = "Матч найден! Подключаемся к игровому серверу..."
			var error = game_client.connect_to_url(json["gameServerUrl"])
			if error != OK:
				status_label.text = "Ошибка подключения к игровому серверу: %s" % error
				find_game_button.disabled = false
				back_button.disabled = false
		else:
			status_label.text = "Ошибка: неверный ответ от матчмейкера."
			find_game_button.disabled = false
			back_button.disabled = false
	elif response_code == 202:
		var json = JSON.parse_string(body.get_string_from_utf8())
		if json and json.has("message"):
			status_label.text = json["message"]
		else:
			status_label.text = "Добавлен в очередь, ожидаем матч..."
		# Не включаем кнопки обратно - игрок остается в очереди
	else:
		status_label.text = "Ошибка подключения к матчмейкеру: %s" % response_code
		find_game_button.disabled = false
		back_button.disabled = false

func _handle_websocket_messages():
	while game_client.get_available_packet_count() > 0:
		var packet = game_client.get_packet()
		var message = packet.get_string_from_utf8()
		var json = JSON.parse_string(message)

		if json and json.has("type"):
			if json.type == "connected":
				status_label.text = "Подключено! Игра началась. Управление: A и D."
				find_game_button.hide()
				back_button.hide()
			elif json.type == "gameState":
				_handle_game_state(json.data)

func _handle_game_state(data):
	if data.has("players"):
		var players_state = data.players
		for player_id_str in players_state:
			var pos = players_state[player_id_str]
			if not player_nodes.has(player_id_str):
				var new_player = ColorRect.new()
				new_player.size = Vector2(50, 50)
				new_player.color = Color.AQUAMARINE if int(player_id_str) % 2 != 0 else Color.CORAL
				add_child(new_player)
				player_nodes[player_id_str] = new_player

			player_nodes[player_id_str].position = Vector2(pos.x, pos.y)

func _send_player_input():
	if game_client.get_ready_state() != WebSocketPeer.STATE_OPEN:
		return

	var action = null
	if Input.is_action_pressed("mp_move_left"):
		action = "left"
	elif Input.is_action_pressed("mp_move_right"):
		action = "right"

	if action:
		var message = JSON.stringify({"type": "input", "action": action})
		game_client.send_text(message)

func _on_back_button_pressed():
	SceneManager.goto_main_menu()
