const STEAM_WEB_API_KEY = "YOUR_STEAM_WEB_API_KEY_HERE";
const APP_ID = 436480;

interface MatchmakingRequest {
  ticket: string;
  steamId: string;
}

interface QueuedPlayer {
  steamId: string;
  timestamp: number;
}

class Matchmaker {
  private queue: QueuedPlayer[] = [];
  private gameServerUrl = "ws://localhost:8001";

  constructor(private port: number) {}

  start() {
    const server = Deno.serve({ port: this.port }, async (req) => {
      if (req.method !== "POST") {
        return new Response("Method not allowed", { status: 405 });
      }

      try {
        const body: MatchmakingRequest = await req.json();
        return await this.handleMatchmakingRequest(body);
      } catch (error) {
        console.error("Error handling request:", error);
        return new Response("Bad request", { status: 400 });
      }
    });

    console.log(`Matchmaker running on port ${this.port}`);
  }

  private async handleMatchmakingRequest(request: MatchmakingRequest): Promise<Response> {
    console.log(`Matchmaking request from Steam ID: ${request.steamId}`);

    // Verify Steam ticket
    const isValid = await this.verifySteamTicket(request.ticket, request.steamId);
    if (!isValid) {
      return new Response(
        JSON.stringify({ error: "Invalid Steam ticket" }),
        {
          status: 401,
          headers: { "Content-Type": "application/json" }
        }
      );
    }

    // Add player to queue
    this.queue.push({
      steamId: request.steamId,
      timestamp: Date.now()
    });

    console.log(`Player ${request.steamId} added to queue. Queue size: ${this.queue.length}`);

    // Check if we can make a match (need at least 2 players)
    if (this.queue.length >= 2) {
      // Match two players
      const player1 = this.queue.shift()!;
      const player2 = this.queue.shift()!;

      console.log(`Match found for players ${player1.steamId} and ${player2.steamId}`);

      return new Response(
        JSON.stringify({
          gameServerUrl: this.gameServerUrl,
          matchId: `match_${Date.now()}`
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" }
        }
      );
    }

    return new Response(
      JSON.stringify({ message: "Added to queue, waiting for match..." }),
      {
        status: 202,
        headers: { "Content-Type": "application/json" }
      }
    );
  }

  private async verifySteamTicket(ticket: string, steamId: string): Promise<boolean> {
    if (STEAM_WEB_API_KEY === "YOUR_STEAM_WEB_API_KEY_HERE") {
      console.warn("WARNING: Using mock Steam verification. Set your real Steam Web API key!");
      return true; // Mock verification for testing
    }

    try {
      const url = `https://api.steampowered.com/ISteamUserAuth/AuthenticateUserTicket/v1/`;
      const params = new URLSearchParams({
        key: STEAM_WEB_API_KEY,
        appid: APP_ID.toString(),
        ticket: ticket
      });

      const response = await fetch(`${url}?${params}`);
      const data = await response.json();

      if (data.response && data.response.params) {
        const verifiedSteamId = data.response.params.steamid;
        return verifiedSteamId === steamId;
      }

      return false;
    } catch (error) {
      console.error("Error verifying Steam ticket:", error);
      return false;
    }
  }
}

const matchmaker = new Matchmaker(8000);
matchmaker.start();
