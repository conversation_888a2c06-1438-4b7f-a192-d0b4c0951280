# Soup Game Backend

Этот бэкенд обеспечивает мультиплеерную функциональность для игры Soup Simple.

## Требования

- [Deno](https://deno.land/) установлен на вашей системе
- Steam Web API Key (опционально, для продакшена)

## Настройка

1. **Steam Web API Key (опционально):**
   - Получите ключ на https://steamcommunity.com/dev/apikey
   - Откройте `matchmaker.ts` и замените `YOUR_STEAM_WEB_API_KEY_HERE` на ваш ключ
   - Для тестирования можно оставить как есть - будет использоваться mock-верификация

## Запуск

Откройте два терминала в папке `soup-game-backend`:

### Терминал 1 - Game Server:
```bash
deno run --allow-net game_server.ts
```

### Терминал 2 - Matchmaker:
```bash
deno run --allow-net matchmaker.ts
```

## Архитектура

- **Matchmaker (порт 8000):** Обрабатывает запросы на поиск игры, верифицирует Steam билеты
- **Game Server (порт 8001):** Обрабатывает игровую логику и WebSocket соединения

## Тестирование

1. Убедитесь, что Steam запущен
2. Запустите оба сервера
3. Запустите игру Godot из редактора
4. Экспортируйте и запустите второй экземпляр игры
5. В обоих клиентах нажмите "ONLINE DEMO" -> "Найти игру"
6. Управляйте квадратами клавишами A и D

## Порты

- 8000: Matchmaker HTTP API
- 8001: Game Server WebSocket

Убедитесь, что эти порты свободны перед запуском.
