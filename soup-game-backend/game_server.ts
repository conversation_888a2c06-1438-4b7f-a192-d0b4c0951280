interface Player {
  id: number;
  x: number;
  y: number;
  socket: WebSocket;
}

interface GameState {
  players: Record<number, { x: number; y: number }>;
}

class GameServer {
  private players: Map<number, Player> = new Map();
  private gameState: GameState = { players: {} };
  private gameLoop: number | null = null;

  constructor(private port: number) {}

  start() {
    const server = Deno.serve({ port: this.port }, (req) => {
      if (req.headers.get("upgrade") !== "websocket") {
        return new Response(null, { status: 501 });
      }

      const { socket, response } = Deno.upgradeWebSocket(req);
      this.handleConnection(socket);
      return response;
    });

    console.log(`Game server running on port ${this.port}`);
    this.startGameLoop();
  }

  private handleConnection(socket: WebSocket) {
    const playerId = Date.now() + Math.random();
    
    socket.onopen = () => {
      console.log(`Player ${playerId} connected`);
      
      const player: Player = {
        id: playerId,
        x: 400 + Math.random() * 200,
        y: 300 + Math.random() * 200,
        socket: socket
      };
      
      this.players.set(playerId, player);
      this.updateGameState();
      
      socket.send(JSON.stringify({
        type: "connected",
        playerId: playerId
      }));
    };

    socket.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        this.handlePlayerInput(playerId, data);
      } catch (error) {
        console.error("Error parsing message:", error);
      }
    };

    socket.onclose = () => {
      console.log(`Player ${playerId} disconnected`);
      this.players.delete(playerId);
      this.updateGameState();
    };

    socket.onerror = (error) => {
      console.error(`WebSocket error for player ${playerId}:`, error);
    };
  }

  private handlePlayerInput(playerId: number, data: any) {
    const player = this.players.get(playerId);
    if (!player || data.type !== "input") return;

    const speed = 5;
    switch (data.action) {
      case "left":
        player.x = Math.max(0, player.x - speed);
        break;
      case "right":
        player.x = Math.min(800, player.x + speed);
        break;
    }
    
    this.updateGameState();
  }

  private updateGameState() {
    this.gameState.players = {};
    for (const [id, player] of this.players) {
      this.gameState.players[id] = { x: player.x, y: player.y };
    }
  }

  private startGameLoop() {
    this.gameLoop = setInterval(() => {
      this.broadcastGameState();
    }, 1000 / 60); // 60 FPS
  }

  private broadcastGameState() {
    if (this.players.size === 0) return;

    const message = JSON.stringify({
      type: "gameState",
      data: this.gameState
    });

    for (const player of this.players.values()) {
      if (player.socket.readyState === WebSocket.OPEN) {
        player.socket.send(message);
      }
    }
  }
}

const gameServer = new GameServer(8001);
gameServer.start();
